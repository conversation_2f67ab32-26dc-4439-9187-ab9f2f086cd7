<template>
  <div class="chart-container wind-chart-layout">
    <div class="chart-title"></div>
    <div class="wind-chart-container">
      <iframe
        ref="windIframe"
        frameborder="0"
        marginheight="0"
        marginwidth="0"
        scrolling="no"
        referrerpolicy="no-referrer"
      ></iframe>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const windIframe = ref<HTMLIFrameElement>()

// 设置iframe地址
const setupIframeUrl = () => {
  const productionUrl =
    'http://***********:8080/clouddddp/#/balanceNewTable?userld=3847091120e111e88818fa163e2609bc'
  const mainUrl = import.meta.env.DEV ? '/test/index.html' : productionUrl

  if (windIframe.value && mainUrl) {
    windIframe.value.src = mainUrl
    console.log('风电图表iframe地址已设置:', mainUrl)
  } else {
    console.warn('风电图表iframe未找到', {
      iframe: !!windIframe.value,
      url: mainUrl,
    })
  }
}

onMounted(() => {
  setupIframeUrl()
})
</script>

<style scoped>
.chart-container {
  overflow: hidden;
  position: relative;
  flex: 1;
}

.wind-chart-layout .chart-title {
  background-image: url('/images/WindBg.webp');
}

.chart-title {
  height: 5vh;
  width: 100%;
  background-size: 100% auto;
  background-position: left top;
  background-repeat: no-repeat;
  flex-shrink: 0;
}

.wind-chart-container {
  height: calc(100% - 5vh);
  position: relative;
  overflow: hidden;
  margin: 0 1.5vh 1vh 1.5vh;
}
</style>
