import { createApp } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import App from './App.vue'

// 路由配置
const routes = [
  {
    path: '/',
    redirect: '/balance-monitoring',
  },
  {
    path: '/about',
    name: 'About',
    component: () => import('./pages/About.vue'),
  },
  {
    path: '/balance-monitoring',
    name: 'BalanceMonitoring',
    component: () => import('./pages/BalanceMonitoring.vue'),
  },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

const app = createApp(App)
app.use(router)
app.mount('#app')
