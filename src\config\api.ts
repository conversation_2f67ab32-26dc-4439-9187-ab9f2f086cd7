/**
 * API配置模块
 * 抽取各个API的配置，便于统一管理和维护
 */

import type { ApiConfig, DateRange } from '@/types/api'

/**
 * API配置
 */
export const API_CONFIG: ApiConfig = {
  // 基础API地址 - 从环境变量获取
  BASE_URL: import.meta.env.VITE_API_BASE_URL || '/dwyztApp/dwyzt',

  // 实时数据API配置
  REALTIME_DATA: {
    url: '/getDataByRtKeyId',
    // 数据类型映射
    DATA_TYPES: {
      REAL_TIME_ACE: '130010:320000000000010034', // 实时ACE
      CURRENT_COST: '130037:320000000000010010', // 当班费用
    },
  },

  // 断面监视API配置
  SECTION_DATA: {
    url: '/getSectionTop5',
  },

  // CPS数据API配置
  CPS_DATA: {
    url: '/getCPSList',
  },
  REALTIME_CPS_DATA: {
    url: '/getDataByRtKeyId',
    DATA_TYPES: {
      CPS1: '130010:320000000000010129', // CPS1
      CPS2: '130010:320000000000010078', // CPS2
    },
  },
}

/**
 * 构建实时数据API URL
 * @param dataTypes 数据类型数组，如 ['REAL_TIME_ACE', 'CURRENT_COST']
 * @returns 完整的API URL
 */
export function buildRealtimeDataUrl(
  dataTypes: string[] = ['REAL_TIME_ACE', 'CURRENT_COST']
): string {
  const rtKeyStr = dataTypes
    .map((type) => API_CONFIG.REALTIME_DATA.DATA_TYPES[type])
    .filter(Boolean)
    .join(',')

  return `${API_CONFIG.BASE_URL}${API_CONFIG.REALTIME_DATA.url}?rtKeyStr=${rtKeyStr}`
}

/**
 * 构建CPS数据API URL
 * @param startDay 开始日期，格式：YYYY-MM-DD
 * @param endDay 结束日期，格式：YYYY-MM-DD
 * @returns 完整的API URL
 */
export function buildCPSDataUrl(startDay: string, endDay: string): string {
  return `${API_CONFIG.BASE_URL}${API_CONFIG.CPS_DATA.url}?startDay=${startDay}&endDay=${endDay}`
}

/**
 * 构建实时CPS数据API URL
 * @param dataTypes 数据类型数组，如 ['CPS1', 'CPS2']
 * @returns 完整的API URL
 */
export function buildRealtimeCPSDataUrl(dataTypes: string[] = ['CPS1', 'CPS2']): string {
  const rtKeyStr = dataTypes
    .map((type) => API_CONFIG.REALTIME_CPS_DATA.DATA_TYPES[type])
    .filter(Boolean)
    .join(',')

  return `${API_CONFIG.BASE_URL}${API_CONFIG.REALTIME_CPS_DATA.url}?rtKeyStr=${rtKeyStr}`
}

/**
 * 构建断面监视数据API URL
 * @returns 完整的API URL
 */
export function buildSectionDataUrl(): string {
  return `${API_CONFIG.BASE_URL}${API_CONFIG.SECTION_DATA.url}`
}

/**
 * 获取默认的CPS查询日期范围（最近7天）
 * @returns 包含startDay和endDay的对象
 */
export function getDefaultCPSDateRange(): DateRange {
  const endDate = new Date()
  const startDate = new Date()
  startDate.setDate(endDate.getDate() - 6) // 最近7天

  const formatDate = (date: Date): string => {
    return date.toISOString().split('T')[0] // YYYY-MM-DD格式
  }

  return {
    startDay: formatDate(startDate),
    endDay: formatDate(endDate),
  }
}
