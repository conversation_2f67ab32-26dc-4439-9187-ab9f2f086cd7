/// <reference types="vite/client" />

// 环境变量类型定义
interface ImportMetaEnv {
  readonly VITE_API_BASE_URL: string
  readonly VITE_APP_TITLE: string
  readonly VITE_APP_VERSION: string
  readonly VITE_CHART_BALANCE: string
  readonly VITE_CHART_WIND: string
  readonly VITE_CHART_SOLAR: string
  readonly VITE_REFRESH_INTERVAL_CPS: string
  readonly VITE_REFRESH_INTERVAL_REALTIME: string
  readonly VITE_REFRESH_INTERVAL_INFO: string
  readonly VITE_REFRESH_INTERVAL_SECTION: string
  readonly VITE_DEBUG: string
  readonly VITE_LOG_LEVEL: string
  readonly VITE_DEV_SERVER_PORT: string
  readonly VITE_DEV_SERVER_HOST: string
  readonly VITE_BUILD_SOURCEMAP: string
  readonly VITE_BUILD_MINIFY: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
