<template>
  <div id="info-container" class="info-container">
    <div class="info-box current-cost">
      <div class="info-box-value">{{ currentCost }}</div>
    </div>
    <div class="info-box real-time-ace">
      <div class="info-box-value">{{ realTimeACE }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ApiService } from '@/services/api'
import type { InfoDisplayData } from '@/types/api'

// 响应式数据
const currentCost = ref<string | number>('')
const realTimeACE = ref<string | number>('')

// 定时器
let refreshTimer: number | null = null
const refreshInterval = 30000 // 30秒刷新一次

// 格式化数字为1位小数
const formatNumber = (value: number | string | null | undefined): string => {
  if (value === null || value === undefined || value === '') {
    return 'N/A'
  }

  const num = parseFloat(value.toString())
  if (isNaN(num)) {
    return value?.toString() || 'N/A'
  }

  return num.toFixed(1)
}

// 转换接口数据为组件格式
const transformApiData = (apiData: number[]): InfoDisplayData => {
  // 根据API文档，返回数据按参数顺序排列
  // 第一个参数是实时ACE (130010:320000000000010034)
  // 第二个参数是当班费用 (130037:320000000000010010)
  return {
    realTimeAce: formatNumber(apiData[0]),
    currentCost: formatNumber(apiData[1]),
  }
}

// 从接口获取数据
const fetchData = async (): Promise<void> => {
  try {
    const result = await ApiService.realtime.getRealtimeData()

    if (result.code === '0000' && result.data && Array.isArray(result.data)) {
      // 转换接口数据为组件格式
      const data = transformApiData(result.data)
      currentCost.value = data.currentCost
      realTimeACE.value = data.realTimeAce
    } else {
      console.error('接口返回错误:', result)
      showError('数据获取失败')
    }
  } catch (error) {
    console.error('接口调用失败:', error)
    showError('网络请求失败')
  }
}

// 显示错误信息
const showError = (message: string): void => {
  currentCost.value = message
  realTimeACE.value = message
}

// 启动自动刷新
const startAutoRefresh = (): void => {
  stopAutoRefresh()

  refreshTimer = window.setInterval(() => {
    console.log('信息显示自动刷新数据...')
    fetchData()
  }, refreshInterval)

  console.log(`信息显示自动刷新已启动，间隔：${refreshInterval / 1000}秒`)
}

// 停止自动刷新
const stopAutoRefresh = (): void => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
    console.log('信息显示自动刷新已停止')
  }
}

// 组件挂载时初始化
onMounted(() => {
  fetchData()
  startAutoRefresh()
  console.log('信息显示组件已初始化')
})

// 组件卸载时清理
onUnmounted(() => {
  stopAutoRefresh()
  console.log('信息显示组件已销毁')
})
</script>

<style></style>
