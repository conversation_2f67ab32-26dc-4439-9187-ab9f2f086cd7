<template>
  <div class="chart-container balance-chart-layout">
    <div class="chart-title"></div>
    <div class="balance-chart-container">
      <iframe
        ref="balanceIframe"
        frameborder="0"
        marginheight="0"
        marginwidth="0"
        scrolling="no"
        referrerpolicy="no-referrer"
      ></iframe>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const balanceIframe = ref<HTMLIFrameElement>()

// 设置iframe地址
const setupIframeUrl = () => {
  const productionUrl =
    'http://***********:8080/clouddddp/#/balanceNewTable?userld=3847091120e111e88818fa163e2609bc'
  const mainUrl = import.meta.env.DEV ? '/test/index.html' : productionUrl

  if (balanceIframe.value && mainUrl) {
    balanceIframe.value.src = mainUrl
    console.log('平衡图表iframe地址已设置:', mainUrl)
  } else {
    console.warn('平衡图表iframe未找到', {
      iframe: !!balanceIframe.value,
      url: mainUrl,
    })
  }
}

onMounted(() => {
  setupIframeUrl()
})
</script>

<style></style>
