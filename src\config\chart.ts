/**
 * 图表配置模块
 * 抽取各个图表组件的公共配置，便于统一管理和维护
 */

import type { ChartSeries } from '@/types/api'

/**
 * 颜色配置选项
 */
export const ColorOptions = {
  green: {
    color: '#00ff88',
    colorTransparent: 'rgba(0, 255, 136, 0.3)',
    colorTransparent2: 'rgba(0, 255, 136, 0.1)',
    gradient: ['rgba(0, 255, 136, 0.8)', 'rgba(0, 255, 136, 0.1)'],
  },
  blue: {
    color: '#00aaff',
    colorTransparent: 'rgba(0, 170, 255, 0.3)',
    colorTransparent2: 'rgba(0, 170, 255, 0.1)',
    gradient: ['rgba(0, 170, 255, 0.8)', 'rgba(0, 170, 255, 0.1)'],
  },
  red: {
    color: '#ff4444',
    colorTransparent: 'rgba(255, 68, 68, 0.3)',
    colorTransparent2: 'rgba(255, 68, 68, 0.1)',
    gradient: ['rgba(255, 68, 68, 0.8)', 'rgba(255, 68, 68, 0.1)'],
  },
  yellow: {
    color: '#ffaa00',
    colorTransparent: 'rgba(255, 170, 0, 0.3)',
    colorTransparent2: 'rgba(255, 170, 0, 0.1)',
    gradient: ['rgba(255, 170, 0, 0.8)', 'rgba(255, 170, 0, 0.1)'],
  },
  purple: {
    color: '#aa44ff',
    colorTransparent: 'rgba(170, 68, 255, 0.3)',
    colorTransparent2: 'rgba(170, 68, 255, 0.1)',
    gradient: ['rgba(170, 68, 255, 0.8)', 'rgba(170, 68, 255, 0.1)'],
  },
}

/**
 * 处理简单系列数据
 */
export function processSimpleSeries(series: ChartSeries[]) {
  return series.map((item) => {
    const colorConfig = ColorOptions[item.colorType] || ColorOptions.blue
    
    return {
      name: item.name,
      type: 'line',
      data: item.data,
      smooth: true,
      showSymbol: item.showSymbol,
      symbolSize: 6,
      lineStyle: {
        width: 2,
        color: colorConfig.color,
      },
      itemStyle: {
        color: colorConfig.color,
        borderColor: colorConfig.color,
        borderWidth: 2,
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: colorConfig.gradient[0] },
            { offset: 1, color: colorConfig.gradient[1] },
          ],
        },
      },
    }
  })
}

/**
 * 创建图表选项
 */
export function createChartOption(config: {
  xAxis: string[]
  series: any[]
  yAxis?: any
  gridConfig?: any
  legendConfig?: any
  xAxisConfig?: any
  yAxisConfig?: any
  seriesConfig?: any
}) {
  const {
    xAxis,
    series,
    yAxis,
    gridConfig = {},
    legendConfig = {},
    xAxisConfig = {},
    yAxisConfig = {},
  } = config

  return {
    backgroundColor: 'transparent',
    grid: {
      top: '15%',
      left: '5%',
      right: '5%',
      bottom: '10%',
      containLabel: true,
      ...gridConfig,
    },
    legend: {
      show: true,
      top: '5%',
      textStyle: {
        color: '#c0c0c0',
        fontSize: 12,
      },
      ...legendConfig,
    },
    xAxis: {
      type: 'category',
      data: xAxis,
      axisLabel: {
        color: '#c0c0c0',
        fontSize: 10,
        rotate: 45,
        interval: 'auto',
      },
      axisLine: {
        lineStyle: {
          color: '#444',
        },
      },
      axisTick: {
        lineStyle: {
          color: '#444',
        },
      },
      ...xAxisConfig,
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#c0c0c0',
        fontSize: 10,
      },
      axisLine: {
        lineStyle: {
          color: '#444',
        },
      },
      axisTick: {
        lineStyle: {
          color: '#444',
        },
      },
      splitLine: {
        lineStyle: {
          color: '#333',
          type: 'dashed',
        },
      },
      ...yAxisConfig,
      ...yAxis,
    },
    series,
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      borderColor: '#444',
      textStyle: {
        color: '#fff',
      },
    },
  }
}
