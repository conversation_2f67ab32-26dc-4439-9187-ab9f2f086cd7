/**
 * API相关的TypeScript类型定义
 */

// 通用API响应结构
export interface ApiResponse<T = any> {
  code: string
  message?: string
  data?: T
}

// CPS数据相关类型
export interface CPSDataItem {
  time: string
  value: number | string
}

export interface CPSApiData {
  CPS1List: CPSDataItem[]
  CPS2List: CPSDataItem[]
}

// 实时数据类型
export interface RealtimeDataItem {
  value: number
  time: string
}

/**
 * actualValue: "819.610718"
    diffValue: "19.610718"
    limitValue:"800.000000"
    maxValue: "925.551147"
    occurTime: "2025-06-19 11:45:57"
    sectionName: "练海2L77线,练海2L78线,熟沙4X35线,熟沙4X36线四线"
    volt: "1005"
    predictionValue: "839.221680"
 */
// 断面监视数据类型
export interface SectionDataItem {
  actualValue: string
  diffValue: string
  limitValue: string
  maxValue: string
  occurTime: string
  sectionName: string
  predictionValue: string
  volt: string
}

// 图表数据类型
export interface ChartSeries {
  name: string
  colorType: 'green' | 'blue' | 'red' | 'yellow' | 'purple'
  showSymbol: boolean
  data: (number | null)[]
}

export interface ChartData {
  xAxis: string[]
  series: ChartSeries[]
  yAxis?: any
}

// API配置类型
export interface ApiConfig {
  BASE_URL: string
  REALTIME_DATA: {
    url: string
    DATA_TYPES: Record<string, string>
  }
  SECTION_DATA: {
    url: string
  }
  CPS_DATA: {
    url: string
  }
  REALTIME_CPS_DATA: {
    url: string
    DATA_TYPES: Record<string, string>
  }
}

// 日期范围类型
export interface DateRange {
  startDay: string
  endDay: string
}

// 实时数据存储类型
export interface RealtimeDataStore {
  CPS1: RealtimeDataItem | null
  CPS2: RealtimeDataItem | null
}

// 信息显示数据类型
export interface InfoDisplayData {
  currentCost: number | string
  realTimeAce: number | string
}
