<template>
  <div class="about-page">
    <div class="container">
      <header class="header">
        <h1>关于</h1>
      </header>
      <main class="main-content">
        <div class="content-card">
          <h2>全网平衡监视系统</h2>
          <p>这是一个用于监控电网平衡状态的可视化系统。</p>
          <div class="navigation">
            <router-link to="/balance-monitoring" class="nav-button">
              返回监控页面
            </router-link>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
// About页面逻辑
</script>

<style scoped>
.about-page {
  width: 100%;
  height: 100vh;
  background-color: #020f22;
  color: #c0c0c0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.container {
  max-width: 800px;
  padding: 2rem;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.header h1 {
  font-size: 2.5rem;
  margin: 0;
  color: #ffffff;
}

.main-content {
  display: flex;
  justify-content: center;
}

.content-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 2rem;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
}

.content-card h2 {
  color: #ffffff;
  margin-bottom: 1rem;
}

.content-card p {
  line-height: 1.6;
  margin-bottom: 2rem;
}

.navigation {
  margin-top: 2rem;
}

.nav-button {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-decoration: none;
  border-radius: 4px;
  transition: transform 0.2s ease;
}

.nav-button:hover {
  transform: translateY(-2px);
}
</style>
