/**
 * 应用常量配置
 */

// 应用信息
export const APP_INFO = {
  TITLE: import.meta.env.VITE_APP_TITLE || '全网平衡监视系统',
  VERSION: import.meta.env.VITE_APP_VERSION || '1.0.0',
} as const

// 刷新间隔配置（毫秒）
export const REFRESH_INTERVALS = {
  CPS: parseInt(import.meta.env.VITE_REFRESH_INTERVAL_CPS) || 60000,
  REALTIME: parseInt(import.meta.env.VITE_REFRESH_INTERVAL_REALTIME) || 1000,
  INFO: parseInt(import.meta.env.VITE_REFRESH_INTERVAL_INFO) || 30000,
  SECTION: parseInt(import.meta.env.VITE_REFRESH_INTERVAL_SECTION) || 60000,
} as const

// 图表URL配置
export const CHART_URLS = {
  BALANCE: import.meta.env.VITE_CHART_BALANCE || '',
  WIND: import.meta.env.VITE_CHART_WIND || '',
  SOLAR: import.meta.env.VITE_CHART_SOLAR || '',
} as const

// 调试配置
export const DEBUG_CONFIG = {
  ENABLED: import.meta.env.VITE_DEBUG === 'true',
  LOG_LEVEL: import.meta.env.VITE_LOG_LEVEL || 'info',
} as const

// 路由路径常量
export const ROUTES = {
  HOME: '/',
  ABOUT: '/about',
  BALANCE_MONITORING: '/balance-monitoring',
} as const

// API响应状态码
export const API_CODES = {
  SUCCESS: '0000',
  ERROR: '9999',
} as const

// 图表配置常量
export const CHART_CONFIG = {
  DEFAULT_HEIGHT: 400,
  DEFAULT_WIDTH: 600,
  ANIMATION_DURATION: 1000,
  TOOLTIP_DELAY: 100,
} as const

// 表格配置常量
export const TABLE_CONFIG = {
  PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
} as const

// 时间格式常量
export const TIME_FORMATS = {
  DATE: 'YYYY-MM-DD',
  TIME: 'HH:mm:ss',
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  DISPLAY_TIME: 'MM-DD HH:mm:ss',
} as const

// 数字格式化配置
export const NUMBER_FORMAT = {
  DECIMAL_PLACES: 1,
  THOUSAND_SEPARATOR: ',',
  DECIMAL_SEPARATOR: '.',
} as const

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络请求失败',
  DATA_ERROR: '数据获取失败',
  FORMAT_ERROR: '数据格式错误',
  CONTAINER_NOT_FOUND: '容器元素不存在',
} as const

// 成功消息
export const SUCCESS_MESSAGES = {
  DATA_LOADED: '数据加载成功',
  REFRESH_SUCCESS: '刷新成功',
} as const
