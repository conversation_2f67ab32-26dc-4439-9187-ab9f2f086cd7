<template>
  <div class="balance-monitoring-page">
    <div class="container">
      <main class="main-grid">
        <!-- 页面标题 -->
        <PageHeader />

        <!-- 上半部分：平衡图表、风电图表、光伏图表 -->
        <div class="charts-row top-layout">
          <!-- 平衡图表区域 -->
          <BalanceChartSection />

          <!-- 右侧图表列 -->
          <div class="charts-column">
            <!-- 风电图表 -->
            <WindChartSection />

            <!-- 光伏图表 -->
            <SolarChartSection />
          </div>
        </div>

        <!-- 下半部分：断面表格和CPS图表 -->
        <div class="charts-row bottom-layout">
          <!-- 断面监视表格 -->
          <SectionTableContainer />

          <!-- 右侧CPS图表和信息显示 -->
          <div class="charts-column right-column">
            <div class="chart-container">
              <!-- CPS图表 -->
              <CPSChartContainer />

              <!-- 信息显示区域 -->
              <InfoDisplayContainer />
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import PageHeader from '@/components/layout/PageHeader.vue'
import BalanceChartSection from '@/components/charts/BalanceChartSection.vue'
import WindChartSection from '@/components/charts/WindChartSection.vue'
import SolarChartSection from '@/components/charts/SolarChartSection.vue'
import SectionTableContainer from '@/components/tables/SectionTableContainer.vue'
import CPSChartContainer from '@/components/charts/CPSChartContainer.vue'
import InfoDisplayContainer from '@/components/info/InfoDisplayContainer.vue'

// 页面初始化逻辑
onMounted(() => {
  console.log('全网平衡监视页面已加载')
})

onUnmounted(() => {
  console.log('全网平衡监视页面已卸载')
})
</script>

<style></style>
