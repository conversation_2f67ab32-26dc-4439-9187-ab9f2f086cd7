<template>
  <div id="cps-chart" ref="chartContainer" class="chart"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import CPSChart from '@/components/charts/CPSChart.ts'

const chartContainer = ref<HTMLDivElement>()
let cpsChart: CPSChart | null = null

onMounted(() => {
  if (chartContainer.value) {
    // 初始化CPS图表组件
    cpsChart = new CPSChart(chartContainer.value)
    console.log('CPS图表组件已初始化')
  }
})

onUnmounted(() => {
  // 清理图表资源
  if (cpsChart) {
    cpsChart.destroy()
    cpsChart = null
    console.log('CPS图表组件已销毁')
  }
})
</script>

<style scoped>
.chart {
  width: 100%;
  height: 60%;
  position: relative;
}
</style>
