<template>
  <div class="table-container section-table-layout">
    <div class="chart-title"></div>
    <div id="section-table" class="section-table-wrapper">
      <div v-if="loading" class="loading-message">加载中...</div>
      <div v-else-if="error" class="error-message">{{ error }}</div>
      <table v-else-if="tableData.columns.length > 0" class="section-table">
        <thead>
          <tr>
            <th v-for="column in tableData.columns" :key="column">
              <span>{{ column }}</span>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(row, index) in tableData.data" :key="index">
            <td v-for="(cell, cellIndex) in row" :key="cellIndex">
              {{ cell }}
            </td>
          </tr>
        </tbody>
      </table>
      <div v-else class="no-data-message">暂无数据</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ApiService } from '@/services/api'
import type { SectionDataItem } from '@/types/api'

// 响应式数据
const loading = ref<boolean>(false)
const error = ref<string>('')
const tableData = ref<{
  columns: string[]
  data: string[][]
}>({
  columns: [],
  data: [],
})

// 定时器
let refreshTimer: number | null = null
const refreshInterval = 60000 // 60秒刷新一次

// 格式化数字为指定小数位数
const formatNumber = (value: number | string | null | undefined, decimals: number = 1): string => {
  if (value === null || value === undefined || value === '') {
    return ' '
  }

  const num = parseFloat(value.toString())
  if (isNaN(num)) {
    return value?.toString() || ' '
  }

  return num.toFixed(decimals)
}

/**
 *
 *
 * @param apiData
 */
// 转换接口数据为表格格式
const transformApiData = (apiData: SectionDataItem[]): { columns: string[]; data: string[][] } => {
  const columns = ['断面名称', '限额', '预测潮流', '差额']
  const data = apiData.map((item) => [
    item.sectionName || '',
    formatNumber(item.limitValue, 0),
    formatNumber(item.predictionValue, 0),
    formatNumber(item.diffValue, 0),
  ])

  return { columns, data }
}

// 从接口获取数据
const fetchData = async (): Promise<void> => {
  loading.value = true
  error.value = ''

  try {
    const result = await ApiService.section.getSectionData()

    if (result.code === '0000' && result.data) {
      // 转换接口数据为表格格式
      const transformedData = transformApiData(result.data)
      tableData.value = transformedData
    } else {
      console.error('接口返回错误:', result)
      error.value = '数据获取失败'
    }
  } catch (err) {
    console.error('接口调用失败:', err)
    error.value = '网络请求失败'
  } finally {
    loading.value = false
  }
}

// 启动自动刷新
const startAutoRefresh = (): void => {
  stopAutoRefresh()

  refreshTimer = window.setInterval(() => {
    console.log('断面表格自动刷新数据...')
    fetchData()
  }, refreshInterval)

  console.log(`断面表格自动刷新已启动，间隔：${refreshInterval / 1000}秒`)
}

// 停止自动刷新
const stopAutoRefresh = (): void => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
    console.log('断面表格自动刷新已停止')
  }
}

// 组件挂载时初始化
onMounted(() => {
  fetchData()
  startAutoRefresh()
  console.log('断面表格组件已初始化')
})

// 组件卸载时清理
onUnmounted(() => {
  stopAutoRefresh()
  console.log('断面表格组件已销毁')
})
</script>

<style></style>
