/**
 * CPS曲线图表组件 - Vue TSX版本
 */
import dayjs from 'dayjs'
import * as echarts from 'echarts'
import type { ECharts } from 'echarts'
import { processSimpleSeries, createChartOption, ColorOptions } from '@/config/chart'
import { buildCPSDataUrl, buildRealtimeCPSDataUrl, getDefaultCPSDateRange } from '@/config/api'
import type { CPSApiData, ChartData, DateRange, RealtimeDataStore, ApiResponse } from '@/types/api'

export default class CPSChart {
  private container: HTMLElement
  private data: ChartData | null = null
  private chart: ECharts | null = null
  private dateRange: DateRange
  private apiUrl: string
  private realtimeApiUrl: string
  private refreshTimer: number | null = null
  private refreshInterval: number = 60000 // 60秒
  private realtimeTimer: number | null = null
  private realtimeInterval: number = 1000 // 1秒
  private realtimeData: RealtimeDataStore = { CPS1: null, CPS2: null }
  private resizeHandler?: () => void
  private mediaQueryHandler?: () => void

  constructor(container: HTMLElement, data: ChartData | null = null, dateRange?: DateRange) {
    this.container = container
    this.data = data
    this.dateRange = dateRange || getDefaultCPSDateRange()
    this.apiUrl = buildCPSDataUrl(this.dateRange.startDay, this.dateRange.endDay)
    this.realtimeApiUrl = buildRealtimeCPSDataUrl()

    this.init()
  }

  private init(): void {
    if (!this.container) {
      console.error('容器元素不存在')
      return
    }

    // 获取设备像素比，用于高分辨率屏幕适配
    const devicePixelRatio = window.devicePixelRatio || 1

    // 初始化ECharts实例，配置高分辨率支持
    this.chart = echarts.init(this.container, null, {
      devicePixelRatio: devicePixelRatio,
      renderer: 'canvas',
      useDirtyRect: true,
    })

    // 如果没有传入数据，则从接口获取
    if (!this.data) {
      this.fetchData()
    } else {
      this.data = this.transformApiData(this.data as any)
      this.render()
    }

    // 启动定时刷新
    this.startAutoRefresh()

    // 启动实时数据获取
    this.startRealtimeDataFetch()

    // 添加窗口大小变化和设备像素比变化的监听器
    this.setupResizeListeners()
  }

  // 设置响应式监听器
  private setupResizeListeners(): void {
    // 窗口大小变化监听器
    this.resizeHandler = () => {
      if (this.chart) {
        this.chart.resize()
      }
    }

    // 设备像素比变化监听器
    this.mediaQueryHandler = () => {
      if (this.chart) {
        const currentDevicePixelRatio = window.devicePixelRatio || 1
        if (currentDevicePixelRatio !== this.chart.getDevicePixelRatio()) {
          console.log('设备像素比变化，重新初始化图表')
          this.reinitializeChart()
        }
      }
    }

    window.addEventListener('resize', this.resizeHandler)

    // 监听设备像素比变化
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia(`(resolution: ${window.devicePixelRatio}dppx)`)
      if (mediaQuery.addEventListener) {
        mediaQuery.addEventListener('change', this.mediaQueryHandler)
      } else if ((mediaQuery as any).addListener) {
        ;(mediaQuery as any).addListener(this.mediaQueryHandler)
      }
    }
  }

  // 重新初始化图表
  private reinitializeChart(): void {
    if (!this.chart) return

    // 保存当前配置
    const currentOption = this.chart.getOption()

    // 销毁旧图表
    this.chart.dispose()

    // 重新创建图表
    const devicePixelRatio = window.devicePixelRatio || 1
    this.chart = echarts.init(this.container, null, {
      devicePixelRatio: devicePixelRatio,
      renderer: 'canvas',
      useDirtyRect: true,
    })

    // 恢复配置
    if (currentOption) {
      this.chart.setOption(currentOption)
    } else if (this.data) {
      this.render()
    }
  }

  // 从接口获取数据
  private async fetchData(): Promise<void> {
    try {
      const response = await fetch(this.apiUrl)
      const result: ApiResponse<CPSApiData> = await response.json()

      if (result.code === '0000' && result.data) {
        // 转换接口数据为图表格式
        this.data = this.transformApiData(result.data)
        this.render()
      } else {
        console.error('CPS接口返回错误:', result)
      }
    } catch (error) {
      console.error('CPS接口调用失败:', error)
    }
  }

  // 获取实时CPS数据
  private async fetchRealtimeData(): Promise<void> {
    try {
      const response = await fetch(this.realtimeApiUrl)
      const result: ApiResponse<number[]> = await response.json()

      if (result.code === '0000' && result.data) {
        this.processRealtimeData(result.data)
      } else {
        console.error('实时CPS接口返回错误:', result)
      }
    } catch (error) {
      console.error('实时CPS接口调用失败:', error)
    }
  }

  // 处理实时数据
  private processRealtimeData(realtimeApiData: number[]): void {
    // 更新实时数据存储
    if (Array.isArray(realtimeApiData)) {
      this.realtimeData.CPS1 = {
        value: this.formatNumber(realtimeApiData[0]),
        time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      }
      this.realtimeData.CPS2 = {
        value: this.formatNumber(realtimeApiData[1]),
        time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      }

      // 将实时数据插入到历史数据中
      this.insertRealtimeDataToHistory()

      // 重新渲染图表
      this.render()

      console.log('实时CPS数据已更新:', this.realtimeData)
    }
  }

  // 将实时数据插入到历史数据中
  private insertRealtimeDataToHistory(): void {
    if (!this.data || !this.data.series) return

    // 使用dayjs生成当前时间字符串
    const timeString = dayjs().format('YYYY-MM-DD HH:mm:ss')
    const displayTime = this.formatTimeDisplay(timeString)

    // 检查是否需要添加新的时间点
    if (!this.data.xAxis.includes(displayTime)) {
      this.data.xAxis.push(displayTime)

      // 为每个系列添加新的数据点
      this.data.series.forEach((series, index) => {
        if (index === 0 && this.realtimeData.CPS1) {
          series.data.push(this.realtimeData.CPS1.value)
        } else if (index === 1 && this.realtimeData.CPS2) {
          series.data.push(this.realtimeData.CPS2.value)
        } else {
          series.data.push(null)
        }
      })
    } else {
      // 更新现有时间点的数据
      const timeIndex = this.data.xAxis.indexOf(displayTime)
      if (timeIndex !== -1) {
        this.data.series.forEach((series, index) => {
          if (index === 0 && this.realtimeData.CPS1) {
            series.data[timeIndex] = this.realtimeData.CPS1.value
          } else if (index === 1 && this.realtimeData.CPS2) {
            series.data[timeIndex] = this.realtimeData.CPS2.value
          }
        })
      }
    }
  }

  // 转换接口数据为图表格式
  private transformApiData(apiData: CPSApiData): ChartData {
    const { CPS1List = [], CPS2List = [] } = apiData

    // 提取所有时间点，去重并排序作为X轴
    const allTimes = [...CPS1List.map((item) => item.time), ...CPS2List.map((item) => item.time)]
    const uniqueTimes = [...new Set(allTimes)].sort()

    // 格式化时间显示
    const xAxis = uniqueTimes.map((time) => this.formatTimeDisplay(time))

    // 创建时间到值的映射
    const timeValueMap1 = new Map<string, number>()
    const timeValueMap2 = new Map<string, number>()

    CPS1List.forEach((item) => {
      timeValueMap1.set(item.time, this.formatNumber(item.value))
    })

    CPS2List.forEach((item) => {
      timeValueMap2.set(item.time, this.formatNumber(item.value))
    })

    // 根据X轴时间生成对应的Y轴数据
    const cps1Data = uniqueTimes.map((time) => timeValueMap1.get(time) || null)
    const cps2Data = uniqueTimes.map((time) => timeValueMap2.get(time) || null)

    return {
      xAxis,
      series: [
        {
          name: '当班CPS1',
          colorType: 'green',
          showSymbol: true,
          data: cps1Data,
        },
        {
          name: '当班CPS2',
          colorType: 'blue',
          showSymbol: true,
          data: cps2Data,
        },
      ],
    }
  }

  // 格式化时间显示
  private formatTimeDisplay(timeString: string): string {
    if (!timeString) return timeString

    try {
      const date = dayjs(timeString)
      if (!date.isValid()) {
        console.warn('无效的时间格式:', timeString)
        return timeString
      }
      return date.format('MM-DD HH:mm:ss')
    } catch (error) {
      console.warn('时间格式化失败:', timeString, error)
      return timeString
    }
  }

  // 格式化数字为1位小数
  private formatNumber(value: number | string): number {
    if (value === null || value === undefined || value === '') {
      return 0
    }

    const num = parseFloat(value.toString())
    if (isNaN(num)) {
      return 0
    }

    return parseFloat(num.toFixed(1))
  }

  // 渲染图表
  private render(): void {
    if (!this.data) {
      this.showError('数据格式错误')
      return
    }

    // 隐藏加载状态
    if (this.chart) {
      this.chart.hideLoading()
    }

    const { xAxis, series, yAxis } = this.data

    // 获取设备像素比用于高分辨率适配
    const devicePixelRatio = window.devicePixelRatio || 1
    const scaleFactor = Math.max(1, devicePixelRatio * 0.8)

    // 使用公共配置处理简单系列数据
    const seriesData = processSimpleSeries(series)

    // 为高分辨率屏幕调整配置
    const highResConfig = this.getHighResolutionConfig(scaleFactor)

    // 使用公共配置创建图表选项
    const option = createChartOption({
      xAxis,
      series: seriesData,
      yAxis,
      gridConfig: {
        top: '20%',
        bottom: '15%',
      },
      legendConfig: highResConfig.legend,
      xAxisConfig: {
        interval: 'auto',
        ...highResConfig.xAxis,
      },
      yAxisConfig: highResConfig.yAxis,
      seriesConfig: highResConfig.series,
    })

    this.chart?.setOption(option)
    this.drawRealtimeDataView(xAxis, seriesData, scaleFactor)
  }

  // 获取高分辨率屏幕配置
  private getHighResolutionConfig(scaleFactor: number) {
    const baseFontSize = 12
    const baseLineWidth = 2
    const baseSymbolSize = 6

    return {
      legend: {
        textStyle: {
          fontSize: Math.round(baseFontSize * scaleFactor),
        },
        itemHeight: Math.round(8 * scaleFactor),
        itemStyle: {
          borderWidth: Math.max(1, Math.round(baseLineWidth * scaleFactor)),
        },
      },
      xAxis: {
        axisLabel: {
          fontSize: Math.round(baseFontSize * scaleFactor),
        },
        axisLine: {
          lineStyle: {
            width: Math.max(1, Math.round(baseLineWidth * scaleFactor * 0.5)),
          },
        },
        axisTick: {
          lineStyle: {
            width: Math.max(1, Math.round(baseLineWidth * scaleFactor * 0.5)),
          },
        },
      },
      yAxis: {
        axisLabel: {
          fontSize: Math.round(baseFontSize * scaleFactor),
        },
        axisLine: {
          lineStyle: {
            width: Math.max(1, Math.round(baseLineWidth * scaleFactor * 0.5)),
          },
        },
        axisTick: {
          lineStyle: {
            width: Math.max(1, Math.round(baseLineWidth * scaleFactor * 0.5)),
          },
        },
        splitLine: {
          lineStyle: {
            width: Math.max(1, Math.round(baseLineWidth * scaleFactor * 0.3)),
          },
        },
      },
      series: {
        lineStyle: {
          width: Math.max(2, Math.round(baseLineWidth * scaleFactor)),
        },
        symbolSize: Math.max(4, Math.round(baseSymbolSize * scaleFactor)),
      },
    }
  }

  // 显示错误信息
  private showError(message: string): void {
    if (this.chart) {
      this.chart.showLoading({
        text: message,
        color: '#c23531',
        textColor: '#c23531',
        maskColor: 'rgba(255, 255, 255, 0.8)',
        zlevel: 0,
      })
    }
  }

  // 绘制实时数据视图
  private drawRealtimeDataView(data: string[], seriesData: any[], scaleFactor: number = 1): void {
    if (!this.chart || !data.length || !seriesData.length) return

    const lastIndex = data.length - 1
    const lastCategory = data[lastIndex]
    const lastCPS1Value = seriesData[0]?.data[lastIndex]
    const lastCPS2Value = seriesData[1]?.data[lastIndex]

    if (lastCPS1Value === undefined || lastCPS2Value === undefined) return

    // 等待图表渲染完成后再计算像素坐标
    setTimeout(() => {
      if (!this.chart) return

      try {
        const pos1 = this.chart.convertToPixel({ seriesIndex: 0 }, [lastCategory, lastCPS1Value])
        const pos2 = this.chart.convertToPixel({ seriesIndex: 1 }, [lastCategory, lastCPS2Value])

        if (!pos1 || !pos2) return

        // 获取图表容器的尺寸
        const chartHeight = this.chart.getHeight()
        const chartWidth = this.chart.getWidth()

        // 配置参数
        const baseGroupWidth = 130
        const baseGroupHeight = 20
        const baseFontSize = 12
        const baseLineWidth = 2
        const baseMinDistance = 30
        const baseMarginFromEdge = 50

        const groupWidth = Math.round(baseGroupWidth * scaleFactor)
        const groupHeight = Math.round(baseGroupHeight * scaleFactor)
        const fontSize = Math.round(baseFontSize * scaleFactor)
        const lineWidth = Math.max(1, Math.round(baseLineWidth * scaleFactor))
        const minDistance = Math.round(baseMinDistance * scaleFactor)
        const marginFromEdge = Math.round(baseMarginFromEdge * scaleFactor)

        // 计算group的最佳位置
        const positions = this.calculateGroupPositions(
          pos1,
          pos2,
          chartHeight,
          groupHeight,
          minDistance,
          marginFromEdge,
        )

        // 计算group中心点坐标
        const group1Center = {
          x: chartWidth - 10 - groupWidth / 2,
          y: positions.group1Top + groupHeight / 2,
        }

        const group2Center = {
          x: chartWidth - 10 - groupWidth / 2,
          y: positions.group2Top + groupHeight / 2,
        }

        this.chart.setOption({
          graphic: {
            elements: [
              // CPS1 实时值显示组
              {
                type: 'group',
                right: 10,
                top: positions.group1Top,
                children: [
                  {
                    type: 'rect',
                    z: 1000,
                    shape: { width: groupWidth, height: groupHeight, r: 3 },
                    style: {
                      fill: ColorOptions.green.colorTransparent2,
                      stroke: ColorOptions.green.color,
                      lineWidth: lineWidth,
                      shadowBlur: Math.round(8 * scaleFactor),
                      shadowColor: 'rgba(0,0,0,0.3)',
                    },
                  },
                  {
                    type: 'text',
                    z: 1001,
                    left: 10 * scaleFactor,
                    top: fontSize / 2,
                    style: {
                      text: `CPS1实时值: ${lastCPS1Value}`,
                      fill: ColorOptions.green.color,
                      font: `${fontSize}px sans-serif`,
                      textAlign: 'left',
                      textVerticalAlign: 'top',
                    },
                  },
                ],
              },
              // CPS2 实时值显示组
              {
                type: 'group',
                right: 10,
                top: positions.group2Top,
                children: [
                  {
                    type: 'rect',
                    z: 1000,
                    shape: { width: groupWidth, height: groupHeight, r: 3 },
                    style: {
                      fill: ColorOptions.blue.colorTransparent2,
                      stroke: ColorOptions.blue.color,
                      lineWidth: lineWidth,
                      shadowBlur: Math.round(8 * scaleFactor),
                      shadowColor: 'rgba(0,0,0,0.3)',
                    },
                  },
                  {
                    type: 'text',
                    z: 1001,
                    left: 10 * scaleFactor,
                    top: fontSize / 2,
                    style: {
                      text: `CPS2实时值: ${lastCPS2Value}`,
                      fill: ColorOptions.blue.color,
                      font: `${fontSize}px sans-serif`,
                      textAlign: 'left',
                      textVerticalAlign: 'top',
                    },
                  },
                ],
              },
              // 箭头线条和箭头头部
              {
                type: 'line',
                z: 999,
                shape: { x1: group1Center.x, y1: group1Center.y, x2: pos1[0], y2: pos1[1] },
                style: {
                  stroke: ColorOptions.green.color,
                  lineWidth: lineWidth,
                  lineDash: [Math.round(5 * scaleFactor), Math.round(5 * scaleFactor)],
                  opacity: 0.8,
                },
              },
              {
                type: 'polygon',
                z: 999,
                shape: {
                  points: this.createArrowHead(
                    group1Center.x,
                    group1Center.y,
                    pos1[0],
                    pos1[1],
                    Math.round(6 * scaleFactor),
                  ),
                },
                style: { fill: ColorOptions.green.color, opacity: 0.8 },
              },
              {
                type: 'line',
                z: 999,
                shape: { x1: group2Center.x, y1: group2Center.y, x2: pos2[0], y2: pos2[1] },
                style: {
                  stroke: ColorOptions.blue.color,
                  lineWidth: lineWidth,
                  lineDash: [Math.round(5 * scaleFactor), Math.round(5 * scaleFactor)],
                  opacity: 0.8,
                },
              },
              {
                type: 'polygon',
                z: 999,
                shape: {
                  points: this.createArrowHead(
                    group2Center.x,
                    group2Center.y,
                    pos2[0],
                    pos2[1],
                    Math.round(6 * scaleFactor),
                  ),
                },
                style: { fill: ColorOptions.blue.color, opacity: 0.8 },
              },
            ],
          },
        })
      } catch (error) {
        console.warn('绘制实时数据视图失败:', error)
      }
    }, 100)
  }

  // 计算group的最佳位置，避免重叠
  private calculateGroupPositions(
    pos1: number[],
    pos2: number[],
    chartHeight: number,
    groupHeight: number,
    minDistance: number,
    marginFromEdge: number,
  ): { group1Top: number; group2Top: number } {
    // 根据数据点的Y坐标确定group的初始位置
    let group1Top: number, group2Top: number

    // 计算数据点在图表中的相对位置
    const pos1Ratio = pos1[1] / chartHeight
    const pos2Ratio = pos2[1] / chartHeight

    // 根据数据点位置智能确定group位置
    if (pos1Ratio < 0.3) {
      group1Top = pos1[1] + 30
    } else if (pos1Ratio > 0.7) {
      group1Top = pos1[1] - groupHeight - 30
    } else {
      group1Top = chartHeight * 0.1
    }

    if (pos2Ratio < 0.3) {
      group2Top = pos2[1] + 30
    } else if (pos2Ratio > 0.7) {
      group2Top = pos2[1] - groupHeight - 30
    } else {
      group2Top = chartHeight * 0.7
    }

    // 确保group不超出图表边界
    group1Top = Math.max(
      marginFromEdge,
      Math.min(group1Top, chartHeight - groupHeight - marginFromEdge),
    )
    group2Top = Math.max(
      marginFromEdge,
      Math.min(group2Top, chartHeight - groupHeight - marginFromEdge),
    )

    // 检查并解决重叠问题
    const distance = Math.abs(group1Top - group2Top)
    if (distance < minDistance) {
      const midPoint = (group1Top + group2Top) / 2
      const halfDistance = minDistance / 2

      if (group1Top < group2Top) {
        group1Top = midPoint - halfDistance - groupHeight / 2
        group2Top = midPoint + halfDistance - groupHeight / 2
      } else {
        group2Top = midPoint - halfDistance - groupHeight / 2
        group1Top = midPoint + halfDistance - groupHeight / 2
      }

      // 再次确保不超出边界
      group1Top = Math.max(
        marginFromEdge,
        Math.min(group1Top, chartHeight - groupHeight - marginFromEdge),
      )
      group2Top = Math.max(
        marginFromEdge,
        Math.min(group2Top, chartHeight - groupHeight - marginFromEdge),
      )
    }

    return { group1Top, group2Top }
  }

  // 创建箭头头部的点坐标
  private createArrowHead(
    x1: number,
    y1: number,
    x2: number,
    y2: number,
    size: number,
  ): number[][] {
    // 计算箭头方向
    const angle = Math.atan2(y2 - y1, x2 - x1)

    // 箭头头部的三个点
    return [
      [x2, y2], // 箭头尖端
      [x2 - size * Math.cos(angle - Math.PI / 6), y2 - size * Math.sin(angle - Math.PI / 6)], // 左侧点
      [x2 - size * Math.cos(angle + Math.PI / 6), y2 - size * Math.sin(angle + Math.PI / 6)], // 右侧点
    ]
  }

  // 启动自动刷新
  private startAutoRefresh(): void {
    this.stopAutoRefresh()

    this.refreshTimer = window.setInterval(() => {
      console.log('CPS图表自动刷新数据...')
      this.refreshData()
    }, this.refreshInterval)

    console.log(`CPS图表自动刷新已启动，间隔：${this.refreshInterval / 1000}秒`)
  }

  // 停止自动刷新
  private stopAutoRefresh(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer)
      this.refreshTimer = null
      console.log('CPS图表自动刷新已停止')
    }
  }

  // 启动实时数据获取
  private startRealtimeDataFetch(): void {
    this.stopRealtimeDataFetch()

    // 立即获取一次实时数据
    this.fetchRealtimeData()

    this.realtimeTimer = window.setInterval(() => {
      console.log('获取实时CPS数据...')
      this.fetchRealtimeData()
    }, this.realtimeInterval)

    console.log(`实时CPS数据获取已启动，间隔：${this.realtimeInterval / 1000}秒`)
  }

  // 停止实时数据获取
  private stopRealtimeDataFetch(): void {
    if (this.realtimeTimer) {
      clearInterval(this.realtimeTimer)
      this.realtimeTimer = null
      console.log('实时CPS数据获取已停止')
    }
  }

  // 公共方法：更新数据
  public updateData(newData: ChartData): void {
    this.data = newData
    this.render()
  }

  // 公共方法：更新日期范围并重新获取数据
  public updateDateRange(startDay: string, endDay: string): void {
    this.dateRange = { startDay, endDay }
    this.apiUrl = buildCPSDataUrl(startDay, endDay)
    this.fetchData()
  }

  // 公共方法：刷新数据（重新从接口获取）
  public refreshData(): void {
    this.fetchData()
  }

  // 公共方法：设置刷新间隔
  public setRefreshInterval(intervalMs: number): void {
    this.refreshInterval = intervalMs
    if (this.refreshTimer) {
      this.startAutoRefresh()
    }
  }

  // 公共方法：设置实时数据获取间隔
  public setRealtimeInterval(intervalMs: number): void {
    this.realtimeInterval = intervalMs
    if (this.realtimeTimer) {
      this.startRealtimeDataFetch()
    }
  }

  // 公共方法：销毁图表
  public destroy(): void {
    // 停止自动刷新
    this.stopAutoRefresh()

    // 停止实时数据获取
    this.stopRealtimeDataFetch()

    // 移除事件监听器
    if (this.resizeHandler) {
      window.removeEventListener('resize', this.resizeHandler)
    }

    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
  }
}
