/**
 * API服务层
 * 封装所有的API调用逻辑
 */

import type { 
  ApiResponse, 
  CPSApiData, 
  SectionDataItem, 
  InfoDisplayData,
  DateRange 
} from '@/types/api'
import { 
  buildRealtimeDataUrl, 
  buildCPSDataUrl, 
  buildRealtimeCPSDataUrl, 
  buildSectionDataUrl,
  getDefaultCPSDateRange 
} from '@/config/api'

/**
 * 通用的fetch请求封装
 */
async function fetchApi<T>(url: string): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(url)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    return await response.json()
  } catch (error) {
    console.error('API请求失败:', error)
    throw error
  }
}

/**
 * CPS数据API服务
 */
export class CPSApiService {
  /**
   * 获取CPS历史数据
   */
  static async getCPSData(dateRange?: DateRange): Promise<ApiResponse<CPSApiData>> {
    const range = dateRange || getDefaultCPSDateRange()
    const url = buildCPSDataUrl(range.startDay, range.endDay)
    return fetchApi<CPSApiData>(url)
  }

  /**
   * 获取实时CPS数据
   */
  static async getRealtimeCPSData(): Promise<ApiResponse<number[]>> {
    const url = buildRealtimeCPSDataUrl(['CPS1', 'CPS2'])
    return fetchApi<number[]>(url)
  }
}

/**
 * 实时数据API服务
 */
export class RealtimeApiService {
  /**
   * 获取实时数据（ACE和费用）
   */
  static async getRealtimeData(): Promise<ApiResponse<number[]>> {
    const url = buildRealtimeDataUrl(['REAL_TIME_ACE', 'CURRENT_COST'])
    return fetchApi<number[]>(url)
  }
}

/**
 * 断面监视API服务
 */
export class SectionApiService {
  /**
   * 获取断面监视数据
   */
  static async getSectionData(): Promise<ApiResponse<SectionDataItem[]>> {
    const url = buildSectionDataUrl()
    return fetchApi<SectionDataItem[]>(url)
  }
}

/**
 * 统一的API服务导出
 */
export const ApiService = {
  cps: CPSApiService,
  realtime: RealtimeApiService,
  section: SectionApiService,
}

export default ApiService
