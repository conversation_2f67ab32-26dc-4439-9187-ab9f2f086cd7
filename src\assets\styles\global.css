/**
 * 全局样式文件
 * 整理自原始的balance-monitoring样式
 */

/* 基础样式重置 */
html {
  box-sizing: border-box;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

body {
  position: relative;
  background-color: #020f22;
  color: #c0c0c0;
  margin: 0;
  padding: 0;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell',
    'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 容器样式 */
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

/* 页面头部样式 */
.header {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 13vh;
  background: url('/images/Title.webp') no-repeat center center;
  background-size: contain;
  z-index: 10;
}

/* 主网格布局 */
.main-grid {
  padding: 7vh 1vh 1vh 1vh;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  gap: 1vh;
}

/* 布局区域 */
.top-layout {
  flex: 5;
  min-height: 0;
}

.bottom-layout {
  flex: 4;
  overflow: auto;
  padding: 1vh;
}

.charts-row {
  display: flex;
  width: 100%;
  flex-wrap: nowrap;
  gap: 1vh;
}

.charts-column {
  display: flex;
  flex-direction: column;
  width: 40%;
  gap: 1vh;
}

.right-column {
  width: 40%;
}

/* 图表容器通用样式 */
.chart-container,
.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  background: #082e6c11;
  border-radius: 8px;
  overflow: hidden;
}

/* 图表标题样式 */
.chart-title {
  height: 5vh;
  width: 100%;
  background-size: 100% auto;
  background-position: left top;
  background-repeat: no-repeat;
  flex-shrink: 0;
}

/* 各个图表的标题背景 */
.balance-chart-layout .chart-title {
  background-image: url('/images/BalanceBg.webp');
}

.wind-chart-layout .chart-title {
  background-image: url('/images/WindBg.webp');
}

.solar-chart-layout .chart-title {
  background-image: url('/images/SolarBg.webp');
}

.section-table-layout .chart-title {
  background-image: url('/images/SectionBg.webp');
}

/* iframe容器样式 */
.balance-chart-container,
.wind-chart-container,
.solar-chart-container {
  height: calc(100% - 5vh);
  position: relative;
  overflow: hidden;
  margin: 0 1.5vh 1vh 1.5vh;
}

/* iframe样式 */
.balance-chart-container iframe,
.wind-chart-container iframe,
.solar-chart-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
  transform-origin: 0 0;
}

/* 平衡图表iframe特殊定位 */
.balance-chart-container iframe {
  width: 4200px;
  height: 3150px;
  top: -160px;
  left: -625px;
  transform: scale(0.86);
}

/* 风电图表iframe特殊定位 */
.wind-chart-container iframe {
  width: 4200px;
  height: 3150px;
  top: -1730px;
  left: -1600.6px;
  transform: scale(0.74);
}

/* 光伏图表iframe特殊定位 */
.solar-chart-container iframe {
  width: 4200px;
  height: 3150px;
  top: -1730px;
  left: -1600.6px;
  transform: scale(0.74);
}

/* CPS图表样式 */
#cps-chart {
  flex: 2;
  padding: 1.5vh;
}

/* 信息显示容器样式 */
.info-container {
  display: flex;
  flex: 1;
  flex-direction: row;
  align-items: center;
}

.info-box {
  text-align: center;
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  position: relative;
  height: 5rem;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: 50%;
}

.current-cost {
  background-image: url('/images/CurrentCost.webp');
}

.real-time-ace {
  background-image: url('/images/RealTimeACE.webp');
}

.info-box-value {
  position: absolute;
  bottom: 14.5%;
  left: 38%;
  font-size: 1.2rem;
  font-weight: bold;
  opacity: 0.8;
}

/* 当班费用文字渐变 */
.current-cost .info-box-value {
  background-image: linear-gradient(to bottom, #ffffff 0%, #d4fafd 30%, #9cf6fe 60%, #00586a 100%);
  -webkit-background-clip: text;
  color: transparent;
}

/* 实时ACE文字渐变 */
.real-time-ace .info-box-value {
  background-image: linear-gradient(to bottom, #ffffff 0%, #e9f0d2 30%, #f9da79 60%, #543e09 100%);
  -webkit-background-clip: text;
  color: transparent;
}

/* 断面表格样式 */
#section-table table {
  padding: 0 1.5vh;
  width: 100%;
  font-size: 1rem;
  border-collapse: separate;
  border-spacing: 0px 0.6vh;
}

#section-table th,
#section-table td {
  padding: 1.5vh;
  text-align: center;
}

#section-table th {
  background: linear-gradient(to bottom, #042e58 0%, #011e3b 15%, #003f6b 100%);
  font-weight: bold;
}

#section-table th span {
  background-image: linear-gradient(to bottom, #ffffff 0%, #ddf1fb 30%, #a9dcfe 60%, #64c8f5 100%);
  -webkit-background-clip: text;
  color: transparent;
}

#section-table td {
  color: #fff;
  background: linear-gradient(to bottom, #042e5893 0%, #011e3b93 15%, #003f6b93 100%);
}

/* 数据标签样式 */
.data-label {
  position: absolute;
  background-color: rgba(10, 26, 51, 0.7);
  border: 1px solid #1a3a66;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.9em;
  color: #fff;
  pointer-events: none;
}

/* 当前值标记样式 */
.current-value {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #fff;
  border: 2px solid;
}

/* 通用消息样式 */
.loading-message,
.error-message,
.no-data-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #c0c0c0;
  font-size: 1rem;
}

.error-message {
  color: #ff6b6b;
}

/* 响应式设计 */
@media screen and (min-width: 2000px) {
  html {
    font-size: 18px;
  }

  .header {
    height: 14vh;
  }

  .main-grid {
    padding: 8vh 1vh 1vh 1vh;
  }

  #section-table table {
    font-size: 1.5rem;
  }

  .info-box {
    height: 6rem;
  }

  .info-box-value {
    font-size: 1.5rem;
    left: 41%;
    bottom: 12%;
  }
}

@media screen and (min-width: 3000px) {
  html {
    font-size: 20px;
  }

  .header {
    height: 14vh;
  }

  .main-grid {
    padding: 8vh 1vh 1vh 1vh;
  }

  #section-table table {
    font-size: 1.5rem;
  }

  .info-box {
    height: 7rem;
  }

  .info-box-value {
    font-size: 1.8rem;
    left: 41%;
    bottom: 10%;
  }
}

@media screen and (min-width: 4000px) {
  html {
    font-size: 28px;
  }

  .info-box-value {
    font-size: 1.8rem;
    left: 39%;
  }
}

/* 小屏幕适配 */
@media screen and (max-width: 1200px) {
  .charts-column {
    width: 45%;
  }

  .right-column {
    width: 55%;
  }

  .info-box-value {
    font-size: 1rem;
  }

  #section-table table {
    font-size: 0.9rem;
  }

  #section-table th,
  #section-table td {
    padding: 1vh;
  }
}

@media screen and (max-width: 768px) {
  .main-grid {
    padding: 5vh 0.5vh 0.5vh 0.5vh;
    gap: 0.5vh;
  }

  .charts-row {
    flex-direction: column;
    gap: 0.5vh;
  }

  .charts-column,
  .right-column {
    width: 100%;
  }

  .info-box {
    height: 4rem;
  }

  .info-box-value {
    font-size: 0.9rem;
    left: 35%;
  }

  #section-table table {
    font-size: 0.8rem;
  }

  #section-table th,
  #section-table td {
    padding: 0.5vh;
  }
}
