/**
 * 工具函数集合
 */

import dayjs from 'dayjs'
import { TIME_FORMATS, NUMBER_FORMAT } from '@/config/constants'

/**
 * 格式化数字
 * @param value 要格式化的值
 * @param decimals 小数位数
 * @param defaultValue 默认值
 * @returns 格式化后的字符串
 */
export function formatNumber(
  value: number | string | null | undefined,
  decimals: number = NUMBER_FORMAT.DECIMAL_PLACES,
  defaultValue: string = 'N/A'
): string {
  if (value === null || value === undefined || value === '') {
    return defaultValue
  }

  const num = parseFloat(value.toString())
  if (isNaN(num)) {
    return value?.toString() || defaultValue
  }

  return num.toFixed(decimals)
}

/**
 * 格式化时间显示
 * @param timeString 时间字符串
 * @param format 格式化模式
 * @returns 格式化后的时间字符串
 */
export function formatTimeDisplay(
  timeString: string,
  format: string = TIME_FORMATS.DISPLAY_TIME
): string {
  if (!timeString) return timeString

  try {
    const date = dayjs(timeString)
    if (!date.isValid()) {
      console.warn('无效的时间格式:', timeString)
      return timeString
    }
    return date.format(format)
  } catch (error) {
    console.warn('时间格式化失败:', timeString, error)
    return timeString
  }
}

/**
 * 获取当前时间字符串
 * @param format 格式化模式
 * @returns 当前时间字符串
 */
export function getCurrentTime(format: string = TIME_FORMATS.DATETIME): string {
  return dayjs().format(format)
}

/**
 * 获取日期范围
 * @param days 天数（负数表示过去的天数）
 * @returns 日期范围对象
 */
export function getDateRange(days: number = -6): { startDay: string; endDay: string } {
  const endDate = dayjs()
  const startDate = endDate.add(days, 'day')

  return {
    startDay: startDate.format(TIME_FORMATS.DATE),
    endDay: endDate.format(TIME_FORMATS.DATE),
  }
}

/**
 * 防抖函数
 * @param func 要防抖的函数
 * @param wait 等待时间（毫秒）
 * @returns 防抖后的函数
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: number | null = null

  return function (...args: Parameters<T>) {
    if (timeout !== null) {
      clearTimeout(timeout)
    }
    timeout = window.setTimeout(() => func(...args), wait)
  }
}

/**
 * 节流函数
 * @param func 要节流的函数
 * @param limit 限制时间（毫秒）
 * @returns 节流后的函数
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean = false

  return function (...args: Parameters<T>) {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

/**
 * 深拷贝对象
 * @param obj 要拷贝的对象
 * @returns 拷贝后的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj
  }

  if (obj instanceof Date) {
    return new Date(obj.getTime()) as T
  }

  if (obj instanceof Array) {
    return obj.map((item) => deepClone(item)) as T
  }

  if (typeof obj === 'object') {
    const clonedObj = {} as T
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }

  return obj
}

/**
 * 检查是否为空值
 * @param value 要检查的值
 * @returns 是否为空
 */
export function isEmpty(value: any): boolean {
  if (value === null || value === undefined) {
    return true
  }

  if (typeof value === 'string') {
    return value.trim() === ''
  }

  if (Array.isArray(value)) {
    return value.length === 0
  }

  if (typeof value === 'object') {
    return Object.keys(value).length === 0
  }

  return false
}

/**
 * 生成唯一ID
 * @param prefix 前缀
 * @returns 唯一ID字符串
 */
export function generateId(prefix: string = 'id'): string {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 获取设备像素比
 * @returns 设备像素比
 */
export function getDevicePixelRatio(): number {
  return window.devicePixelRatio || 1
}

/**
 * 检查是否为移动设备
 * @returns 是否为移动设备
 */
export function isMobile(): boolean {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  )
}

/**
 * 获取浏览器信息
 * @returns 浏览器信息对象
 */
export function getBrowserInfo(): {
  name: string
  version: string
  userAgent: string
} {
  const userAgent = navigator.userAgent
  let name = 'Unknown'
  let version = 'Unknown'

  if (userAgent.indexOf('Chrome') > -1) {
    name = 'Chrome'
    version = userAgent.match(/Chrome\/(\d+)/)?.[1] || 'Unknown'
  } else if (userAgent.indexOf('Firefox') > -1) {
    name = 'Firefox'
    version = userAgent.match(/Firefox\/(\d+)/)?.[1] || 'Unknown'
  } else if (userAgent.indexOf('Safari') > -1) {
    name = 'Safari'
    version = userAgent.match(/Version\/(\d+)/)?.[1] || 'Unknown'
  } else if (userAgent.indexOf('Edge') > -1) {
    name = 'Edge'
    version = userAgent.match(/Edge\/(\d+)/)?.[1] || 'Unknown'
  }

  return { name, version, userAgent }
}

/**
 * 日志工具
 */
export const logger = {
  info: (message: string, ...args: any[]) => {
    console.log(`[INFO] ${message}`, ...args)
  },
  warn: (message: string, ...args: any[]) => {
    console.warn(`[WARN] ${message}`, ...args)
  },
  error: (message: string, ...args: any[]) => {
    console.error(`[ERROR] ${message}`, ...args)
  },
  debug: (message: string, ...args: any[]) => {
    if (import.meta.env.VITE_DEBUG === 'true') {
      console.debug(`[DEBUG] ${message}`, ...args)
    }
  },
}
